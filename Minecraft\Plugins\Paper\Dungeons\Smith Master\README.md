# Smith Master Plugin

A comprehensive Minecraft Paper plugin for the **Luminary Kingdom** dungeon server, featuring an interactive NPC smith with auto-speaking capabilities, GUI shop system, and immersive sound effects similar to Hypixel NPCs.

## 🏰 Server Information
- **Server**: Luminary Kingdom
- **Owner**: MyckasP (Denmark)
- **Version**: 1.0.0
- **Minecraft Version**: 1.20.4
- **API**: Paper

## ✨ Features

### 🤖 Interactive NPC System
- **Right-click Interactions**: Players can right-click the Smith Master NPC to open the shop
- **Auto-speak Feature**: NPC automatically speaks to players within 5 blocks using predefined dialogue
- **Smart Behavior**: NPCs look at players during interactions and prevent damage
- **Professional Appearance**: Villager-based NPC with custom name, equipment, and weaponsmith profession

### 💬 Dialogue System
The NPC uses exactly these dialogue lines as requested:
1. "Greetings, adventurer! What can I do for you today?"
2. "Greetings! How are you doing today? Good luck on your adventures!"
3. "Ah, another adventurer! You'll need proper equipment if you want to survive the dungeons."
4. "My forge burns hot today! Need a new blade? My steel is the finest in the kingdom!"
5. "I've been smithing weapons since before you were born. Trust in my craftsmanship!"
6. "The Goblin King's minions grow stronger. Make sure your gear is up to the challenge!"

### 🛒 GUI Shop System
- **Inventory-based Interface**: Clean, organized shop GUI with 54 slots
- **Future-ready Architecture**: Designed for easy expansion with weapons, armor, and upgrades
- **Placeholder Items**: Currently shows framework with "Coming Soon" sections
- **Interactive Elements**: Click handling and sound effects for all interactions

### 🔊 Sound Effects
- **NPC Speaking**: Villager ambient sounds when NPCs talk
- **Shop Interactions**: Chest open/close sounds for shop GUI
- **Interaction Feedback**: Various sounds for different player actions
- **Configurable**: All sounds can be customized or disabled

### 💾 Data Storage
- **Organized Files**: All data stored in `/plugins/SmithMaster/data/` folder
- **NPC Persistence**: NPC locations and settings saved automatically
- **Player Statistics**: Track player interactions and usage statistics
- **Configuration Files**: Easy-to-edit YAML files for all settings

## 🚀 Installation

1. **Download**: Get the latest `smith-master-1.0.0.jar` from the releases
2. **Install**: Place the JAR file in your server's `plugins` folder
3. **Restart**: Restart your Paper server
4. **Configure**: Edit the configuration files in `/plugins/SmithMaster/`
5. **Spawn NPC**: Use `/smithmaster spawn` to create your first NPC

## 🎮 Commands

### Player Commands
- `/smithmaster help` - Show help message
- `/smithmaster info` - Show plugin information

### Admin Commands (requires `smithmaster.admin` permission)
- `/smithmaster spawn` - Spawn an NPC at your location
- `/smithmaster remove <id|nearest>` - Remove an NPC
- `/smithmaster list` - List all active NPCs
- `/smithmaster stats` - Show plugin statistics
- `/smithmaster test <sounds|shop|dialogue>` - Test plugin features

### Reload Command (requires `smithmaster.reload` permission)
- `/smithmaster reload` - Reload plugin configuration

## 🔧 Configuration

### Main Config (`config.yml`)
```yaml
npc:
  name: "&6&lSmith Master"
  auto-speak-radius: 5.0
  auto-speak-interval: 200
  interaction-cooldown: 20

sounds:
  enabled: true
  npc-speak: "ENTITY_VILLAGER_AMBIENT"
  shop-open: "BLOCK_CHEST_OPEN"
  shop-close: "BLOCK_CHEST_CLOSE"
  volume: 1.0
  pitch: 1.0

shop:
  title: "&6&lSmith Master - Weapons & Armor"
  size: 54
  fill-empty-slots: true
```

### Dialogue Config (`dialogue.yml`)
Contains all NPC dialogue lines and speaking behavior settings.

## 🛡️ Permissions

- `smithmaster.use` - Allow interaction with NPCs (default: true)
- `smithmaster.shop` - Allow access to shop (default: true)
- `smithmaster.admin` - Admin commands (default: op)
- `smithmaster.reload` - Reload configuration (default: op)

## 🔨 Building from Source

### Prerequisites
- Java 17 or higher
- Maven 3.6 or higher
- Paper API 1.20.4

### Build Steps
1. Clone the repository
2. Run `build.bat` (Windows) or use Maven directly:
   ```bash
   mvn clean package
   ```
3. Find the compiled JAR in the `target` folder

## 🎯 Future Expansion Plans

The plugin is designed with future expansion in mind:

### Planned Features
- **Weapon Shop**: Various swords, axes, and ranged weapons
- **Armor Shop**: Different armor tiers and sets
- **Upgrade System**: Enhance existing gear with materials
- **Player Progression**: Unlock higher tiers based on dungeon progress
- **Economy Integration**: Connect with server economy plugins
- **Custom Items**: Unique weapons and armor with special abilities

### Integration Ready
- **Dungeon Plugins**: Easy integration with other dungeon systems
- **Permission Systems**: Full permission support for different player groups
- **Database Support**: Can be extended to use external databases
- **Multi-NPC Support**: Support for multiple NPCs with different specializations

## 📊 Technical Details

### Architecture
- **Modular Design**: Separate managers for different functionalities
- **Event-driven**: Efficient event handling for player interactions
- **Async Tasks**: Non-blocking auto-speak system
- **Data Persistence**: Reliable data storage with backup systems

### Performance
- **Optimized**: Minimal server impact with efficient algorithms
- **Scalable**: Supports multiple NPCs and concurrent players
- **Memory Efficient**: Smart cleanup of unused data
- **Thread Safe**: Proper synchronization for multi-threaded operations

## 🐛 Troubleshooting

### Common Issues
1. **NPC not spawning**: Check console for errors, ensure proper permissions
2. **No sounds**: Verify sound settings in config.yml
3. **Shop not opening**: Check player permissions and console logs
4. **Dialogue not showing**: Verify dialogue.yml file exists and is valid

### Debug Mode
Enable debug mode in config.yml:
```yaml
debug:
  enabled: true
  log-interactions: true
  log-auto-speak: true
```

## 📞 Support

For support, bug reports, or feature requests:
- **Server**: Luminary Kingdom
- **Developer**: MyckasP
- **Location**: Denmark

## 📄 License

This plugin is created specifically for the Luminary Kingdom server. All rights reserved.

---

**Created with ❤️ by MyckasP for Luminary Kingdom**
