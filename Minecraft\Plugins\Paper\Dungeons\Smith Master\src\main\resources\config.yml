# Smith Master Plugin Configuration
# Created by MyckasP for Luminary Kingdom
# 
# This configuration file controls all aspects of the Smith Master NPC
# including dialogue, sounds, shop settings, and behavior.

# NPC Settings
npc:
  # Display name of the NPC (supports color codes with &)
  name: "&6&lSmith Master"
  
  # Radius in blocks for auto-speak feature
  auto-speak-radius: 5.0
  
  # Interval between auto-speak messages (in ticks, 20 ticks = 1 second)
  auto-speak-interval: 200
  
  # Cooldown between player interactions (in ticks)
  interaction-cooldown: 20

# Sound Settings
sounds:
  # Enable/disable all sounds
  enabled: true
  
  # Sound played when N<PERSON> speaks (Minecraft sound name)
  npc-speak: "ENTITY_VILLAGER_AMBIENT"
  
  # Sound played when shop opens
  shop-open: "BLOCK_CHEST_OPEN"
  
  # Sound played when shop closes
  shop-close: "BLOCK_CHEST_CLOSE"
  
  # Volume of sounds (0.0 to 1.0)
  volume: 1.0
  
  # Pitch of sounds (0.5 to 2.0)
  pitch: 1.0

# Shop Settings
shop:
  # Title of the shop GUI (supports color codes with &)
  title: "&6&lSmith Master - Weapons & Armor"
  
  # Size of the shop inventory (must be multiple of 9, max 54)
  size: 54
  
  # Fill empty slots with filler items
  fill-empty-slots: true
  
  # Material for filler items
  filler-item: "GRAY_STAINED_GLASS_PANE"

# Server Information
server:
  name: "Luminary Kingdom"
  owner: "MyckasP"
  country: "Denmark"

# Debug Settings (for development)
debug:
  enabled: false
  log-interactions: false
  log-auto-speak: false
