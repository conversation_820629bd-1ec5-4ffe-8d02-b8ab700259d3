package dk.luminarykingdom.smithmaster.tasks;

import dk.luminarykingdom.smithmaster.SmithMasterPlugin;
import dk.luminarykingdom.smithmaster.npc.SmithMasterNPC;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

/**
 * Handles automatic speaking for Smith Master NPCs
 * 
 * This task runs periodically to make NPCs speak to nearby players
 * using the configured dialogue lines. Includes repeat prevention
 * and random dialogue selection.
 * 
 * <AUTHOR>
 */
public class AutoSpeakTask extends BukkitRunnable {
    
    private final SmithMasterPlugin plugin;
    private final Random random;
    private final Map<UUID, Long> lastSpeakTimes;
    private final Map<UUID, String> lastDialogueLines;
    
    public AutoSpeakTask(@NotNull SmithMasterPlugin plugin) {
        this.plugin = plugin;
        this.random = new Random();
        this.lastSpeakTimes = new HashMap<>();
        this.lastDialogueLines = new HashMap<>();
    }
    
    @Override
    public void run() {
        try {
            // Clean up invalid NPCs first
            plugin.getNPCManager().cleanupInvalidNPCs();
            
            // Get all players near NPCs
            Map<SmithMasterNPC, List<Player>> nearbyPlayers = plugin.getNPCManager().getPlayersNearNPCs();
            
            if (nearbyPlayers.isEmpty()) {
                return; // No players near any NPCs
            }
            
            // Process each NPC with nearby players
            for (Map.Entry<SmithMasterNPC, List<Player>> entry : nearbyPlayers.entrySet()) {
                SmithMasterNPC npc = entry.getKey();
                List<Player> players = entry.getValue();
                
                if (!npc.isValid() || players.isEmpty()) {
                    continue;
                }
                
                // Check if this NPC should speak
                if (shouldNPCSpeak(npc)) {
                    speakToPlayers(npc, players);
                }
            }
            
        } catch (Exception e) {
            plugin.getLogger().severe("Error in AutoSpeakTask: " + e.getMessage());
            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * Check if an NPC should speak based on timing and configuration
     * 
     * @param npc The NPC to check
     * @return true if the NPC should speak
     */
    private boolean shouldNPCSpeak(@NotNull SmithMasterNPC npc) {
        UUID npcId = npc.getEntityId();
        long currentTime = System.currentTimeMillis();
        
        // Check if enough time has passed since last speak
        Long lastSpeak = lastSpeakTimes.get(npcId);
        if (lastSpeak != null) {
            long timeSinceLastSpeak = currentTime - lastSpeak;
            long intervalMs = plugin.getConfigManager().getAutoSpeakInterval() * 50; // Convert ticks to ms
            
            if (timeSinceLastSpeak < intervalMs) {
                return false;
            }
        }
        
        // Random chance to speak (adds some variation)
        return random.nextDouble() < 0.7; // 70% chance to speak when interval is met
    }
    
    /**
     * Make an NPC speak to nearby players
     * 
     * @param npc The NPC that should speak
     * @param players The players to speak to
     */
    private void speakToPlayers(@NotNull SmithMasterNPC npc, @NotNull List<Player> players) {
        // Get dialogue configuration
        List<String> dialogueLines = plugin.getConfigManager().getDialogueLines();
        if (dialogueLines.isEmpty()) {
            plugin.getLogger().warning("No dialogue lines configured for Smith Master NPC!");
            return;
        }
        
        // Select a dialogue line
        String selectedLine = selectDialogueLine(npc, dialogueLines);
        if (selectedLine == null || selectedLine.trim().isEmpty()) {
            return;
        }
        
        // Format the message
        String prefix = plugin.getConfigManager().getDialoguePrefix();
        String fullMessage = prefix + selectedLine;
        
        // Send message to all nearby players
        for (Player player : players) {
            player.sendMessage(fullMessage);
            
            // Play sound for each player
            plugin.getSoundManager().playNPCSpeakSound(player);
            
            // Make NPC look at a random player occasionally
            if (random.nextDouble() < 0.3) { // 30% chance
                npc.lookAt(player.getLocation());
            }
        }
        
        // Update last speak time and dialogue
        UUID npcId = npc.getEntityId();
        lastSpeakTimes.put(npcId, System.currentTimeMillis());
        lastDialogueLines.put(npcId, selectedLine);
        
        // Log if debug is enabled
        if (plugin.getConfig().getBoolean("debug.log-auto-speak", false)) {
            plugin.getLogger().info(String.format("NPC %s spoke to %d players: %s", 
                npcId.toString().substring(0, 8), 
                players.size(), 
                selectedLine));
        }
    }
    
    /**
     * Select an appropriate dialogue line for an NPC
     * 
     * @param npc The NPC that will speak
     * @param dialogueLines Available dialogue lines
     * @return The selected dialogue line, or null if none available
     */
    private String selectDialogueLine(@NotNull SmithMasterNPC npc, @NotNull List<String> dialogueLines) {
        if (dialogueLines.isEmpty()) {
            return null;
        }
        
        // If only one line available, use it
        if (dialogueLines.size() == 1) {
            return dialogueLines.get(0);
        }
        
        // Check if repeat prevention is enabled
        boolean preventRepeats = plugin.getConfigManager().isRepeatPrevention();
        UUID npcId = npc.getEntityId();
        
        if (preventRepeats) {
            String lastLine = lastDialogueLines.get(npcId);
            
            if (lastLine != null) {
                // Check if enough time has passed to allow repeats
                Long lastSpeak = lastSpeakTimes.get(npcId);
                if (lastSpeak != null) {
                    long timeSinceLastSpeak = System.currentTimeMillis() - lastSpeak;
                    long preventionTimeMs = plugin.getConfigManager().getRepeatPreventionTime() * 50;
                    
                    if (timeSinceLastSpeak < preventionTimeMs) {
                        // Remove the last used line from selection
                        List<String> availableLines = dialogueLines.stream()
                            .filter(line -> !line.equals(lastLine))
                            .toList();
                        
                        if (!availableLines.isEmpty()) {
                            return availableLines.get(random.nextInt(availableLines.size()));
                        }
                    }
                }
            }
        }
        
        // Random selection from all lines
        if (plugin.getConfigManager().isRandomDialogueSelection()) {
            return dialogueLines.get(random.nextInt(dialogueLines.size()));
        } else {
            // Sequential selection (cycle through lines)
            String lastLine = lastDialogueLines.get(npcId);
            if (lastLine != null) {
                int lastIndex = dialogueLines.indexOf(lastLine);
                if (lastIndex != -1 && lastIndex < dialogueLines.size() - 1) {
                    return dialogueLines.get(lastIndex + 1);
                }
            }
            return dialogueLines.get(0); // Start from beginning
        }
    }
    
    /**
     * Clear the speak history for an NPC
     * 
     * @param npcId The NPC ID to clear history for
     */
    public void clearNPCHistory(@NotNull UUID npcId) {
        lastSpeakTimes.remove(npcId);
        lastDialogueLines.remove(npcId);
    }
    
    /**
     * Clear all speak history
     */
    public void clearAllHistory() {
        lastSpeakTimes.clear();
        lastDialogueLines.clear();
    }
    
    /**
     * Get debug information about the auto-speak system
     * 
     * @return Debug information string
     */
    @NotNull
    public String getDebugInfo() {
        return String.format("AutoSpeakTask{tracked_npcs=%d, interval=%d_ticks, prevention_enabled=%b}", 
            lastSpeakTimes.size(),
            plugin.getConfigManager().getAutoSpeakInterval(),
            plugin.getConfigManager().isRepeatPrevention());
    }
}
