package dk.luminarykingdom.smithmaster.config;

import dk.luminarykingdom.smithmaster.SmithMasterPlugin;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.logging.Level;

/**
 * Manages all configuration files for the Smith Master plugin
 * 
 * Handles loading, saving, and accessing configuration data including
 * main config, dialogue lines, and NPC settings.
 * 
 * <AUTHOR>
 */
public class ConfigManager {
    
    private final SmithMasterPlugin plugin;
    private FileConfiguration config;
    private FileConfiguration dialogueConfig;
    
    // Configuration file objects
    private File configFile;
    private File dialogueFile;
    
    public ConfigManager(@NotNull SmithMasterPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Load all configuration files
     * @return true if all configs loaded successfully
     */
    public boolean loadConfigs() {
        try {
            // Create plugin data folder if it doesn't exist
            if (!plugin.getDataFolder().exists()) {
                plugin.getDataFolder().mkdirs();
            }
            
            // Load main config
            loadMainConfig();
            
            // Load dialogue config
            loadDialogueConfig();
            
            return true;
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to load configuration files", e);
            return false;
        }
    }
    
    /**
     * Load the main configuration file
     */
    private void loadMainConfig() {
        configFile = new File(plugin.getDataFolder(), "config.yml");
        
        if (!configFile.exists()) {
            plugin.saveDefaultConfig();
        }
        
        config = plugin.getConfig();
        
        // Set default values if they don't exist
        setDefaultConfigValues();
        
        // Save config to ensure all defaults are written
        plugin.saveConfig();
    }
    
    /**
     * Load the dialogue configuration file
     */
    private void loadDialogueConfig() {
        dialogueFile = new File(plugin.getDataFolder(), "dialogue.yml");
        
        if (!dialogueFile.exists()) {
            createDefaultDialogueConfig();
        }
        
        dialogueConfig = YamlConfiguration.loadConfiguration(dialogueFile);
    }
    
    /**
     * Set default configuration values
     */
    private void setDefaultConfigValues() {
        // NPC Settings
        config.addDefault("npc.name", "&6&lSmith Master");
        config.addDefault("npc.auto-speak-radius", 5.0);
        config.addDefault("npc.auto-speak-interval", 200L); // 10 seconds in ticks
        config.addDefault("npc.interaction-cooldown", 20L); // 1 second in ticks
        
        // Sound Settings
        config.addDefault("sounds.enabled", true);
        config.addDefault("sounds.npc-speak", "ENTITY_VILLAGER_AMBIENT");
        config.addDefault("sounds.shop-open", "BLOCK_CHEST_OPEN");
        config.addDefault("sounds.shop-close", "BLOCK_CHEST_CLOSE");
        config.addDefault("sounds.volume", 1.0f);
        config.addDefault("sounds.pitch", 1.0f);
        
        // Shop Settings
        config.addDefault("shop.title", "&6&lSmith Master - Weapons & Armor");
        config.addDefault("shop.size", 54); // 6 rows
        config.addDefault("shop.fill-empty-slots", true);
        config.addDefault("shop.filler-item", "GRAY_STAINED_GLASS_PANE");
        
        // Server Information
        config.addDefault("server.name", "Luminary Kingdom");
        config.addDefault("server.owner", "MyckasP");
        config.addDefault("server.country", "Denmark");
        
        config.options().copyDefaults(true);
    }
    
    /**
     * Create the default dialogue configuration file
     */
    private void createDefaultDialogueConfig() {
        try {
            dialogueFile.createNewFile();
            dialogueConfig = YamlConfiguration.loadConfiguration(dialogueFile);
            
            // Add the required dialogue lines exactly as specified
            dialogueConfig.set("dialogue.lines", List.of(
                "Greetings, adventurer! What can I do for you today?",
                "Greetings! How are you doing today? Good luck on your adventures!",
                "Ah, another adventurer! You'll need proper equipment if you want to survive the dungeons.",
                "My forge burns hot today! Need a new blade? My steel is the finest in the kingdom!",
                "I've been smithing weapons since before you were born. Trust in my craftsmanship!",
                "The Goblin King's minions grow stronger. Make sure your gear is up to the challenge!"
            ));
            
            // Add configuration for dialogue behavior
            dialogueConfig.set("dialogue.prefix", "&6&l[Smith Master] &r&7");
            dialogueConfig.set("dialogue.random-selection", true);
            dialogueConfig.set("dialogue.repeat-prevention", true);
            dialogueConfig.set("dialogue.repeat-prevention-time", 300L); // 15 seconds in ticks
            
            dialogueConfig.save(dialogueFile);
            
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to create dialogue configuration file", e);
        }
    }
    
    /**
     * Reload all configuration files
     */
    public void reloadConfigs() {
        plugin.reloadConfig();
        config = plugin.getConfig();
        dialogueConfig = YamlConfiguration.loadConfiguration(dialogueFile);
    }
    
    // Getter methods for configuration values
    
    public String getNPCName() {
        return config.getString("npc.name", "&6&lSmith Master");
    }
    
    public double getAutoSpeakRadius() {
        return config.getDouble("npc.auto-speak-radius", 5.0);
    }
    
    public long getAutoSpeakInterval() {
        return config.getLong("npc.auto-speak-interval", 200L);
    }
    
    public long getInteractionCooldown() {
        return config.getLong("npc.interaction-cooldown", 20L);
    }
    
    public boolean areSoundsEnabled() {
        return config.getBoolean("sounds.enabled", true);
    }
    
    public String getNPCSpeakSound() {
        return config.getString("sounds.npc-speak", "ENTITY_VILLAGER_AMBIENT");
    }
    
    public String getShopOpenSound() {
        return config.getString("sounds.shop-open", "BLOCK_CHEST_OPEN");
    }
    
    public String getShopCloseSound() {
        return config.getString("sounds.shop-close", "BLOCK_CHEST_CLOSE");
    }
    
    public float getSoundVolume() {
        return (float) config.getDouble("sounds.volume", 1.0);
    }
    
    public float getSoundPitch() {
        return (float) config.getDouble("sounds.pitch", 1.0);
    }
    
    public String getShopTitle() {
        return config.getString("shop.title", "&6&lSmith Master - Weapons & Armor");
    }
    
    public int getShopSize() {
        return config.getInt("shop.size", 54);
    }
    
    public List<String> getDialogueLines() {
        return dialogueConfig.getStringList("dialogue.lines");
    }
    
    public String getDialoguePrefix() {
        return dialogueConfig.getString("dialogue.prefix", "&6&l[Smith Master] &r&7");
    }
    
    public boolean isRandomDialogueSelection() {
        return dialogueConfig.getBoolean("dialogue.random-selection", true);
    }
    
    public boolean isRepeatPrevention() {
        return dialogueConfig.getBoolean("dialogue.repeat-prevention", true);
    }
    
    public long getRepeatPreventionTime() {
        return dialogueConfig.getLong("dialogue.repeat-prevention-time", 300L);
    }
}
