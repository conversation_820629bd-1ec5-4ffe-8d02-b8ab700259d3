package dk.luminarykingdom.smithmaster.shop;

import dk.luminarykingdom.smithmaster.SmithMasterPlugin;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.TextDecoration;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Represents the GUI interface for the Smith Master shop
 * 
 * Handles inventory creation, item placement, click events,
 * and player interactions with the shop interface.
 * 
 * <AUTHOR>
 */
public class ShopGUI implements Listener {
    
    private final SmithMasterPlugin plugin;
    private final Player player;
    private final Inventory inventory;
    private final Map<Integer, ShopItem> slotItems;
    private final List<ShopItem> items;
    private boolean isOpen;
    private boolean isClosing; // Flag to prevent recursive closing
    
    public ShopGUI(@NotNull SmithMasterPlugin plugin, @NotNull Player player) {
        this.plugin = plugin;
        this.player = player;
        this.slotItems = new HashMap<>();
        this.items = new ArrayList<>();
        this.isOpen = false;
        this.isClosing = false;
        
        // Create inventory
        String title = plugin.getConfigManager().getShopTitle();
        int size = plugin.getConfigManager().getShopSize();
        
        Component titleComponent = LegacyComponentSerializer.legacyAmpersand()
            .deserialize(title)
            .decoration(TextDecoration.ITALIC, false);
        
        this.inventory = Bukkit.createInventory(null, size, titleComponent);
        
        // Fill with filler items if configured
        if (plugin.getConfig().getBoolean("shop.fill-empty-slots", true)) {
            fillEmptySlots();
        }
    }
    
    /**
     * Open the shop GUI for the player
     *
     * @return true if the GUI was opened successfully
     */
    public boolean open() {
        // Prevent opening if already open or closing
        if (isOpen || isClosing) {
            return false;
        }

        try {
            // Register event listener
            plugin.getServer().getPluginManager().registerEvents(this, plugin);

            // Open inventory for player
            player.openInventory(inventory);
            isOpen = true;

            return true;

        } catch (Exception e) {
            plugin.getLogger().severe("Failed to open shop GUI for " + player.getName() + ": " + e.getMessage());
            // Clean up on failure
            HandlerList.unregisterAll(this);
            isOpen = false;
            return false;
        }
    }
    
    /**
     * Close the shop GUI
     */
    public void close() {
        if (isOpen && !isClosing) {
            isClosing = true; // Set flag to prevent recursive calls

            try {
                // Unregister events first to prevent onInventoryClose from firing
                HandlerList.unregisterAll(this);

                // Close the inventory
                player.closeInventory();

                // Update state
                isOpen = false;

                // Play close sound
                plugin.getSoundManager().playShopCloseSound(player);

            } finally {
                isClosing = false; // Always reset the flag
            }
        }
    }
    
    /**
     * Add an item to the shop GUI
     * 
     * @param item The shop item to add
     */
    public void addItem(@NotNull ShopItem item) {
        items.add(item);
        
        int slot = item.getSlot();
        if (slot >= 0 && slot < inventory.getSize()) {
            // Use specified slot
            inventory.setItem(slot, item.getDisplayItem());
            slotItems.put(slot, item);
        } else {
            // Find next available slot
            for (int i = 0; i < inventory.getSize(); i++) {
                if (inventory.getItem(i) == null || isFillerItem(inventory.getItem(i))) {
                    inventory.setItem(i, item.getDisplayItem());
                    slotItems.put(i, item);
                    break;
                }
            }
        }
    }
    
    /**
     * Remove an item from the shop GUI
     * 
     * @param item The shop item to remove
     */
    public void removeItem(@NotNull ShopItem item) {
        items.remove(item);
        
        // Find and remove from inventory
        for (Map.Entry<Integer, ShopItem> entry : new HashMap<>(slotItems).entrySet()) {
            if (entry.getValue().matches(item)) {
                int slot = entry.getKey();
                inventory.setItem(slot, createFillerItem());
                slotItems.remove(slot);
            }
        }
    }
    
    /**
     * Clear all items from the shop
     */
    public void clearItems() {
        items.clear();
        slotItems.clear();
        inventory.clear();
        
        if (plugin.getConfig().getBoolean("shop.fill-empty-slots", true)) {
            fillEmptySlots();
        }
    }
    
    /**
     * Refresh the GUI display
     */
    public void refresh() {
        // Clear and rebuild inventory
        inventory.clear();
        slotItems.clear();
        
        // Fill with filler items first
        if (plugin.getConfig().getBoolean("shop.fill-empty-slots", true)) {
            fillEmptySlots();
        }
        
        // Add all items back
        for (ShopItem item : items) {
            addItemToInventory(item);
        }
    }
    
    /**
     * Fill empty slots with filler items
     */
    private void fillEmptySlots() {
        ItemStack fillerItem = createFillerItem();
        
        for (int i = 0; i < inventory.getSize(); i++) {
            if (inventory.getItem(i) == null) {
                inventory.setItem(i, fillerItem);
            }
        }
    }
    
    /**
     * Create a filler item for empty slots
     * 
     * @return The filler item
     */
    private ItemStack createFillerItem() {
        String materialName = plugin.getConfig().getString("shop.filler-item", "GRAY_STAINED_GLASS_PANE");
        Material material;
        
        try {
            material = Material.valueOf(materialName.toUpperCase());
        } catch (IllegalArgumentException e) {
            material = Material.GRAY_STAINED_GLASS_PANE;
        }
        
        ItemStack filler = new ItemStack(material);
        ItemMeta meta = filler.getItemMeta();
        
        if (meta != null) {
            meta.displayName(Component.text(" ").decoration(TextDecoration.ITALIC, false));
            filler.setItemMeta(meta);
        }
        
        return filler;
    }
    
    /**
     * Check if an item is a filler item
     * 
     * @param item The item to check
     * @return true if the item is a filler item
     */
    private boolean isFillerItem(ItemStack item) {
        if (item == null) return false;
        
        String materialName = plugin.getConfig().getString("shop.filler-item", "GRAY_STAINED_GLASS_PANE");
        Material fillerMaterial;
        
        try {
            fillerMaterial = Material.valueOf(materialName.toUpperCase());
        } catch (IllegalArgumentException e) {
            fillerMaterial = Material.GRAY_STAINED_GLASS_PANE;
        }
        
        return item.getType() == fillerMaterial && 
               item.getItemMeta() != null && 
               item.getItemMeta().hasDisplayName();
    }
    
    /**
     * Add an item directly to the inventory
     * 
     * @param item The shop item to add
     */
    private void addItemToInventory(@NotNull ShopItem item) {
        int slot = item.getSlot();
        if (slot >= 0 && slot < inventory.getSize()) {
            inventory.setItem(slot, item.getDisplayItem());
            slotItems.put(slot, item);
        }
    }
    
    /**
     * Handle inventory click events
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!event.getInventory().equals(inventory)) {
            return;
        }
        
        // Cancel all clicks to prevent item movement
        event.setCancelled(true);
        
        if (!(event.getWhoClicked() instanceof Player clickedPlayer) || 
            !clickedPlayer.equals(player)) {
            return;
        }
        
        int slot = event.getSlot();
        ShopItem clickedItem = slotItems.get(slot);
        
        if (clickedItem == null) {
            return; // Clicked on filler item or empty slot
        }
        
        // Handle item purchase/interaction
        handleItemClick(clickedPlayer, clickedItem);
    }
    
    /**
     * Handle inventory close events
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onInventoryClose(InventoryCloseEvent event) {
        // Check if this is our inventory and we're not already closing
        if (!event.getInventory().equals(inventory) || isClosing) {
            return;
        }

        if (event.getPlayer().equals(player) && isOpen) {
            // Set flags to prevent recursive calls
            isClosing = true;
            isOpen = false;

            try {
                // Unregister events to prevent further event handling
                HandlerList.unregisterAll(this);

                // Notify shop manager to clean up (without calling close again)
                plugin.getShopManager().removePlayerShop(player);

                // Play close sound
                plugin.getSoundManager().playShopCloseSound(player);

            } finally {
                isClosing = false;
            }
        }
    }
    
    /**
     * Handle clicking on a shop item
     * 
     * @param player The player who clicked
     * @param item The shop item that was clicked
     */
    private void handleItemClick(@NotNull Player player, @NotNull ShopItem item) {
        if (!item.isPurchasable()) {
            // Info item or non-purchasable item
            player.sendMessage("§7" + item.getName() + " - Information only");
            return;
        }
        
        if (item.isFree()) {
            // Free item
            player.sendMessage("§aThis item is free! (Purchase system coming soon)");
            return;
        }
        
        // Paid item - show purchase message
        player.sendMessage("§7Purchase system coming soon!");
        player.sendMessage("§7Item: §6" + item.getName());
        player.sendMessage("§7Price: §6" + item.getPrice() + " coins");
    }
    
    /**
     * Check if the GUI is currently open
     *
     * @return true if the GUI is open
     */
    public boolean isOpen() {
        return isOpen && !isClosing;
    }
    
    /**
     * Get the player this GUI belongs to
     * 
     * @return The player
     */
    @NotNull
    public Player getPlayer() {
        return player;
    }
    
    /**
     * Get the number of items in the shop
     * 
     * @return The number of shop items
     */
    public int getItemCount() {
        return items.size();
    }
}
