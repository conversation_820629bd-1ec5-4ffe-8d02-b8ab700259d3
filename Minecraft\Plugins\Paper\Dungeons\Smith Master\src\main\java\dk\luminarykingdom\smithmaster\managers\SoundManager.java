package dk.luminarykingdom.smithmaster.managers;

import dk.luminarykingdom.smithmaster.SmithMasterPlugin;
import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.logging.Level;

/**
 * Manages all sound effects for the Smith Master plugin
 * 
 * Handles playing sounds for NPC interactions, shop events,
 * and other plugin activities. Includes fallback sounds
 * and error handling for invalid sound names.
 * 
 * <AUTHOR>
 */
public class SoundManager {
    
    private final SmithMasterPlugin plugin;
    
    public SoundManager(@NotNull SmithMasterPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Play the NPC speak sound for a player
     * 
     * @param player The player to play the sound for
     */
    public void playNPCSpeakSound(@NotNull Player player) {
        if (!plugin.getConfigManager().areSoundsEnabled()) {
            return;
        }
        
        String soundName = plugin.getConfigManager().getNPCSpeakSound();
        Sound sound = parseSound(soundName, Sound.ENTITY_VILLAGER_AMBIENT);
        
        playSound(player, sound);
    }
    
    /**
     * Play the NPC speak sound at a specific location
     * 
     * @param location The location to play the sound at
     */
    public void playNPCSpeakSoundAtLocation(@NotNull Location location) {
        if (!plugin.getConfigManager().areSoundsEnabled()) {
            return;
        }
        
        String soundName = plugin.getConfigManager().getNPCSpeakSound();
        Sound sound = parseSound(soundName, Sound.ENTITY_VILLAGER_AMBIENT);
        
        playSoundAtLocation(location, sound);
    }
    
    /**
     * Play the shop open sound for a player
     * 
     * @param player The player to play the sound for
     */
    public void playShopOpenSound(@NotNull Player player) {
        if (!plugin.getConfigManager().areSoundsEnabled()) {
            return;
        }
        
        String soundName = plugin.getConfigManager().getShopOpenSound();
        Sound sound = parseSound(soundName, Sound.BLOCK_CHEST_OPEN);
        
        playSound(player, sound);
    }
    
    /**
     * Play the shop close sound for a player
     * 
     * @param player The player to play the sound for
     */
    public void playShopCloseSound(@NotNull Player player) {
        if (!plugin.getConfigManager().areSoundsEnabled()) {
            return;
        }
        
        String soundName = plugin.getConfigManager().getShopCloseSound();
        Sound sound = parseSound(soundName, Sound.BLOCK_CHEST_CLOSE);
        
        playSound(player, sound);
    }
    
    /**
     * Play a custom sound for a player
     * 
     * @param player The player to play the sound for
     * @param soundName The name of the sound to play
     */
    public void playCustomSound(@NotNull Player player, @NotNull String soundName) {
        if (!plugin.getConfigManager().areSoundsEnabled()) {
            return;
        }
        
        Sound sound = parseSound(soundName, null);
        if (sound != null) {
            playSound(player, sound);
        }
    }
    
    /**
     * Play a sound for a player with default volume and pitch
     * 
     * @param player The player to play the sound for
     * @param sound The sound to play
     */
    private void playSound(@NotNull Player player, @NotNull Sound sound) {
        try {
            float volume = plugin.getConfigManager().getSoundVolume();
            float pitch = plugin.getConfigManager().getSoundPitch();
            
            player.playSound(player.getLocation(), sound, volume, pitch);
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "Failed to play sound " + sound.name() + " for player " + player.getName(), e);
        }
    }
    
    /**
     * Play a sound at a specific location
     * 
     * @param location The location to play the sound at
     * @param sound The sound to play
     */
    private void playSoundAtLocation(@NotNull Location location, @NotNull Sound sound) {
        try {
            if (location.getWorld() == null) {
                return;
            }
            
            float volume = plugin.getConfigManager().getSoundVolume();
            float pitch = plugin.getConfigManager().getSoundPitch();
            
            location.getWorld().playSound(location, sound, volume, pitch);
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "Failed to play sound " + sound.name() + " at location " + location, e);
        }
    }
    
    /**
     * Parse a sound name string to a Sound enum
     * 
     * @param soundName The sound name to parse
     * @param fallback The fallback sound if parsing fails
     * @return The parsed sound, or fallback if parsing failed
     */
    @Nullable
    private Sound parseSound(@NotNull String soundName, @Nullable Sound fallback) {
        try {
            // Try to parse as enum name
            return Sound.valueOf(soundName.toUpperCase());
            
        } catch (IllegalArgumentException e) {
            // Try common variations
            String normalizedName = soundName.toUpperCase()
                .replace("MINECRAFT:", "")
                .replace(".", "_")
                .replace("-", "_");
            
            try {
                return Sound.valueOf(normalizedName);
            } catch (IllegalArgumentException e2) {
                plugin.getLogger().warning("Invalid sound name: " + soundName + ". Using fallback sound.");
                return fallback;
            }
        }
    }
    
    /**
     * Play a sound effect for item interactions
     * 
     * @param player The player to play the sound for
     * @param soundType The type of interaction sound
     */
    public void playInteractionSound(@NotNull Player player, @NotNull InteractionSoundType soundType) {
        if (!plugin.getConfigManager().areSoundsEnabled()) {
            return;
        }
        
        Sound sound = switch (soundType) {
            case PURCHASE_SUCCESS -> Sound.ENTITY_EXPERIENCE_ORB_PICKUP;
            case PURCHASE_FAIL -> Sound.ENTITY_VILLAGER_NO;
            case ITEM_HOVER -> Sound.UI_BUTTON_CLICK;
            case PAGE_TURN -> Sound.ITEM_BOOK_PAGE_TURN;
            case ERROR -> Sound.BLOCK_NOTE_BLOCK_BASS;
        };
        
        playSound(player, sound);
    }
    
    /**
     * Play ambient smithing sounds at a location
     * 
     * @param location The location to play ambient sounds at
     */
    public void playAmbientSmithingSounds(@NotNull Location location) {
        if (!plugin.getConfigManager().areSoundsEnabled()) {
            return;
        }
        
        // Play random smithing sounds
        Sound[] smithingSounds = {
            Sound.BLOCK_ANVIL_USE,
            Sound.BLOCK_ANVIL_HIT,
            Sound.ITEM_FLINTANDSTEEL_USE,
            Sound.BLOCK_FIRE_AMBIENT
        };
        
        Sound randomSound = smithingSounds[(int) (Math.random() * smithingSounds.length)];
        
        try {
            if (location.getWorld() != null) {
                float volume = plugin.getConfigManager().getSoundVolume() * 0.3f; // Quieter for ambient
                float pitch = 0.8f + (float) (Math.random() * 0.4f); // Random pitch variation
                
                location.getWorld().playSound(location, randomSound, volume, pitch);
            }
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "Failed to play ambient smithing sound", e);
        }
    }
    
    /**
     * Test all configured sounds
     * 
     * @param player The player to test sounds for
     */
    public void testAllSounds(@NotNull Player player) {
        player.sendMessage("§6Testing Smith Master sounds...");
        
        // Test NPC speak sound
        player.sendMessage("§7Testing NPC speak sound...");
        playNPCSpeakSound(player);
        
        // Schedule other sounds with delays
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            player.sendMessage("§7Testing shop open sound...");
            playShopOpenSound(player);
        }, 40L); // 2 seconds
        
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            player.sendMessage("§7Testing shop close sound...");
            playShopCloseSound(player);
        }, 80L); // 4 seconds
        
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            player.sendMessage("§7Testing interaction sounds...");
            playInteractionSound(player, InteractionSoundType.PURCHASE_SUCCESS);
        }, 120L); // 6 seconds
        
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            playInteractionSound(player, InteractionSoundType.PURCHASE_FAIL);
        }, 140L); // 7 seconds
        
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            player.sendMessage("§aSound test completed!");
        }, 180L); // 9 seconds
    }
    
    /**
     * Enum for different types of interaction sounds
     */
    public enum InteractionSoundType {
        PURCHASE_SUCCESS,
        PURCHASE_FAIL,
        ITEM_HOVER,
        PAGE_TURN,
        ERROR
    }
    
    /**
     * Check if sounds are enabled
     * 
     * @return true if sounds are enabled
     */
    public boolean areSoundsEnabled() {
        return plugin.getConfigManager().areSoundsEnabled();
    }
    
    /**
     * Get debug information about the sound system
     * 
     * @return Debug information string
     */
    @NotNull
    public String getDebugInfo() {
        return String.format("SoundManager{enabled=%b, volume=%.2f, pitch=%.2f, npc_sound=%s}", 
            areSoundsEnabled(),
            plugin.getConfigManager().getSoundVolume(),
            plugin.getConfigManager().getSoundPitch(),
            plugin.getConfigManager().getNPCSpeakSound());
    }
}
