package dk.luminarykingdom.smithmaster.listeners;

import dk.luminarykingdom.smithmaster.SmithMasterPlugin;
import dk.luminarykingdom.smithmaster.npc.SmithMasterNPC;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.player.PlayerInteractEntityEvent;
import org.bukkit.inventory.EquipmentSlot;
import org.jetbrains.annotations.NotNull;

/**
 * Handles all interactions with Smith Master NPCs
 * 
 * This listener manages right-click interactions, damage prevention,
 * and other entity-related events for the Smith Master NPCs.
 * 
 * <AUTHOR>
 */
public class NPCInteractionListener implements Listener {
    
    private final SmithMasterPlugin plugin;
    
    public NPCInteractionListener(@NotNull SmithMasterPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Handle player right-click interactions with NPCs
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerInteractEntity(PlayerInteractEntityEvent event) {
        // Only handle main hand interactions to prevent double events
        if (event.getHand() != EquipmentSlot.HAND) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // Check if the clicked entity is a Smith Master NPC
        SmithMasterNPC npc = plugin.getNPCManager().getNPCByEntity(event.getRightClicked());
        if (npc == null) {
            return;
        }
        
        // Cancel the event to prevent default villager behavior
        event.setCancelled(true);
        
        // Check if player has permission to interact
        if (!player.hasPermission("smithmaster.use")) {
            player.sendMessage("§cYou don't have permission to interact with the Smith Master!");
            return;
        }
        
        // Check interaction cooldown
        if (!plugin.getNPCManager().canPlayerInteract(player)) {
            return; // Silently ignore if on cooldown
        }
        
        // Set interaction cooldown
        plugin.getNPCManager().setPlayerInteractionCooldown(player);
        
        // Make NPC look at the player
        npc.lookAt(player.getLocation());
        
        // Play interaction sound
        plugin.getSoundManager().playNPCSpeakSound(player);
        
        // Open the shop GUI
        plugin.getShopManager().openShop(player);
        
        // Log interaction if debug is enabled
        if (plugin.getConfig().getBoolean("debug.log-interactions", false)) {
            plugin.getLogger().info(String.format("Player %s interacted with Smith Master NPC at %s", 
                player.getName(), 
                npc.getLocation() != null ? npc.getLocation().toString() : "unknown"));
        }
    }
    
    /**
     * Prevent NPCs from taking damage
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamage(EntityDamageEvent event) {
        // Check if the damaged entity is a Smith Master NPC
        if (plugin.getNPCManager().isSmithMasterNPC(event.getEntity())) {
            event.setCancelled(true);
        }
    }
    
    /**
     * Prevent NPCs from taking damage from players and make them look at attackers
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // Check if the damaged entity is a Smith Master NPC
        SmithMasterNPC npc = plugin.getNPCManager().getNPCByEntity(event.getEntity());
        if (npc == null) {
            return;
        }
        
        // Cancel the damage
        event.setCancelled(true);
        
        // If damaged by a player, make the NPC look at them and send a message
        if (event.getDamager() instanceof Player player) {
            npc.lookAt(player.getLocation());
            
            // Send a message from the NPC
            String message = plugin.getConfigManager().getDialoguePrefix() + 
                "Hey! I'm trying to help you, not fight you!";
            player.sendMessage(message);
            
            // Play a sound
            plugin.getSoundManager().playNPCSpeakSound(player);
        }
    }
}
