package dk.luminarykingdom.smithmaster.shop;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.TextDecoration;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Represents an item in the Smith Master shop
 * 
 * Contains all information about a shop item including display item,
 * name, lore, price, and slot position. Designed for easy expansion
 * with additional properties like requirements, categories, etc.
 * 
 * <AUTHOR>
 */
public class ShopItem {
    
    private final ItemStack displayItem;
    private final String name;
    private final List<String> lore;
    private final double price;
    private final int slot;
    private final String category;
    private final boolean purchasable;
    
    /**
     * Create a new shop item
     * 
     * @param displayItem The item to display in the GUI
     * @param name The display name of the item
     * @param lore The lore/description lines
     * @param price The price of the item (0 for free/info items)
     * @param slot The GUI slot position (-1 for auto-placement)
     */
    public ShopItem(@NotNull ItemStack displayItem, @NotNull String name, 
                   @NotNull List<String> lore, double price, int slot) {
        this(displayItem, name, lore, price, slot, "general", price > 0);
    }
    
    /**
     * Create a new shop item with category and purchasable flag
     * 
     * @param displayItem The item to display in the GUI
     * @param name The display name of the item
     * @param lore The lore/description lines
     * @param price The price of the item
     * @param slot The GUI slot position (-1 for auto-placement)
     * @param category The category this item belongs to
     * @param purchasable Whether this item can be purchased
     */
    public ShopItem(@NotNull ItemStack displayItem, @NotNull String name, 
                   @NotNull List<String> lore, double price, int slot, 
                   @NotNull String category, boolean purchasable) {
        this.displayItem = displayItem.clone();
        this.name = name;
        this.lore = new ArrayList<>(lore);
        this.price = price;
        this.slot = slot;
        this.category = category;
        this.purchasable = purchasable;
        
        // Apply the name and lore to the display item
        updateDisplayItem();
    }
    
    /**
     * Update the display item with current name and lore
     */
    private void updateDisplayItem() {
        ItemMeta meta = displayItem.getItemMeta();
        if (meta == null) return;
        
        // Set display name
        Component nameComponent = LegacyComponentSerializer.legacyAmpersand()
            .deserialize(name)
            .decoration(TextDecoration.ITALIC, false);
        meta.displayName(nameComponent);
        
        // Set lore
        List<Component> loreComponents = new ArrayList<>();
        for (String loreLine : lore) {
            Component loreComponent = LegacyComponentSerializer.legacyAmpersand()
                .deserialize(loreLine)
                .decoration(TextDecoration.ITALIC, false);
            loreComponents.add(loreComponent);
        }
        
        // Add price information if purchasable
        if (purchasable && price > 0) {
            loreComponents.add(Component.empty());
            loreComponents.add(LegacyComponentSerializer.legacyAmpersand()
                .deserialize("§7Price: §6" + formatPrice(price))
                .decoration(TextDecoration.ITALIC, false));
            loreComponents.add(LegacyComponentSerializer.legacyAmpersand()
                .deserialize("§7Click to purchase!")
                .decoration(TextDecoration.ITALIC, false));
        } else if (!purchasable) {
            loreComponents.add(Component.empty());
            loreComponents.add(LegacyComponentSerializer.legacyAmpersand()
                .deserialize("§8This item is not for sale")
                .decoration(TextDecoration.ITALIC, false));
        }
        
        meta.lore(loreComponents);
        displayItem.setItemMeta(meta);
    }
    
    /**
     * Format the price for display
     * 
     * @param price The price to format
     * @return Formatted price string
     */
    private String formatPrice(double price) {
        if (price == 0) {
            return "Free";
        } else if (price == (int) price) {
            return String.valueOf((int) price) + " coins";
        } else {
            return String.format("%.2f coins", price);
        }
    }
    
    /**
     * Get the display item for the GUI
     * 
     * @return A copy of the display item
     */
    @NotNull
    public ItemStack getDisplayItem() {
        return displayItem.clone();
    }
    
    /**
     * Get the item name
     * 
     * @return The item name
     */
    @NotNull
    public String getName() {
        return name;
    }
    
    /**
     * Get the item lore
     * 
     * @return A copy of the lore list
     */
    @NotNull
    public List<String> getLore() {
        return new ArrayList<>(lore);
    }
    
    /**
     * Get the item price
     * 
     * @return The item price
     */
    public double getPrice() {
        return price;
    }
    
    /**
     * Get the GUI slot position
     * 
     * @return The slot position, or -1 for auto-placement
     */
    public int getSlot() {
        return slot;
    }
    
    /**
     * Get the item category
     * 
     * @return The item category
     */
    @NotNull
    public String getCategory() {
        return category;
    }
    
    /**
     * Check if this item can be purchased
     * 
     * @return true if the item is purchasable
     */
    public boolean isPurchasable() {
        return purchasable;
    }
    
    /**
     * Check if this item is free
     * 
     * @return true if the item price is 0
     */
    public boolean isFree() {
        return price == 0;
    }
    
    /**
     * Create a copy of this shop item with a different slot
     * 
     * @param newSlot The new slot position
     * @return A new ShopItem with the specified slot
     */
    @NotNull
    public ShopItem withSlot(int newSlot) {
        return new ShopItem(displayItem, name, lore, price, newSlot, category, purchasable);
    }
    
    /**
     * Create a copy of this shop item with a different price
     * 
     * @param newPrice The new price
     * @return A new ShopItem with the specified price
     */
    @NotNull
    public ShopItem withPrice(double newPrice) {
        return new ShopItem(displayItem, name, lore, newPrice, slot, category, newPrice > 0);
    }
    
    /**
     * Check if this shop item matches another item by name and category
     * 
     * @param other The other shop item to compare
     * @return true if the items match
     */
    public boolean matches(@Nullable ShopItem other) {
        if (other == null) return false;
        return name.equals(other.name) && category.equals(other.category);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ShopItem shopItem = (ShopItem) obj;
        return Double.compare(shopItem.price, price) == 0 &&
               slot == shopItem.slot &&
               purchasable == shopItem.purchasable &&
               Objects.equals(displayItem, shopItem.displayItem) &&
               Objects.equals(name, shopItem.name) &&
               Objects.equals(lore, shopItem.lore) &&
               Objects.equals(category, shopItem.category);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(displayItem, name, lore, price, slot, category, purchasable);
    }
    
    @Override
    public String toString() {
        return String.format("ShopItem{name='%s', category='%s', price=%.2f, slot=%d, purchasable=%b}", 
            name, category, price, slot, purchasable);
    }
}
