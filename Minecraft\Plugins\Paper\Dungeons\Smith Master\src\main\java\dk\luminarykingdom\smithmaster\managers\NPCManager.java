package dk.luminarykingdom.smithmaster.managers;

import dk.luminarykingdom.smithmaster.SmithMasterPlugin;
import dk.luminarykingdom.smithmaster.npc.SmithMasterNPC;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Manages all Smith Master NPCs in the server
 * 
 * Handles spawning, removing, and tracking NPC entities.
 * Provides methods for interaction detection and NPC management.
 * 
 * <AUTHOR>
 */
public class NPCManager {
    
    private final SmithMasterPlugin plugin;
    private final Map<UUID, SmithMasterNPC> npcs;
    private final Map<UUID, Long> playerInteractionCooldowns;
    
    public NPCManager(@NotNull SmithMasterPlugin plugin) {
        this.plugin = plugin;
        this.npcs = new HashMap<>();
        this.playerInteractionCooldowns = new HashMap<>();
    }
    
    /**
     * Spawn a new Smith Master NPC at the specified location
     * 
     * @param location The location to spawn the NPC
     * @return The spawned NPC, or null if spawning failed
     */
    @Nullable
    public SmithMasterNPC spawnNPC(@NotNull Location location) {
        try {
            SmithMasterNPC npc = new SmithMasterNPC(plugin, location);
            
            if (npc.spawn()) {
                npcs.put(npc.getEntityId(), npc);
                plugin.getLogger().info("Spawned Smith Master NPC at " + formatLocation(location));
                return npc;
            } else {
                plugin.getLogger().warning("Failed to spawn Smith Master NPC at " + formatLocation(location));
                return null;
            }
            
        } catch (Exception e) {
            plugin.getLogger().severe("Error spawning NPC: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Remove an NPC by its entity ID
     * 
     * @param entityId The entity ID of the NPC to remove
     * @return true if the NPC was found and removed
     */
    public boolean removeNPC(@NotNull UUID entityId) {
        SmithMasterNPC npc = npcs.get(entityId);
        if (npc != null) {
            npc.remove();
            npcs.remove(entityId);
            plugin.getLogger().info("Removed Smith Master NPC with ID: " + entityId);
            return true;
        }
        return false;
    }
    
    /**
     * Remove all NPCs from the server
     */
    public void removeAllNPCs() {
        List<UUID> npcIds = new ArrayList<>(npcs.keySet());
        for (UUID entityId : npcIds) {
            removeNPC(entityId);
        }
        plugin.getLogger().info("Removed all Smith Master NPCs");
    }
    
    /**
     * Get an NPC by its entity ID
     * 
     * @param entityId The entity ID to search for
     * @return The NPC if found, null otherwise
     */
    @Nullable
    public SmithMasterNPC getNPC(@NotNull UUID entityId) {
        return npcs.get(entityId);
    }
    
    /**
     * Get an NPC by the Bukkit entity
     * 
     * @param entity The entity to search for
     * @return The NPC if found, null otherwise
     */
    @Nullable
    public SmithMasterNPC getNPCByEntity(@NotNull Entity entity) {
        return npcs.get(entity.getUniqueId());
    }
    
    /**
     * Get all active NPCs
     * 
     * @return A list of all active NPCs
     */
    @NotNull
    public List<SmithMasterNPC> getAllNPCs() {
        return new ArrayList<>(npcs.values());
    }
    
    /**
     * Check if a player can interact with NPCs (not on cooldown)
     * 
     * @param player The player to check
     * @return true if the player can interact
     */
    public boolean canPlayerInteract(@NotNull Player player) {
        UUID playerId = player.getUniqueId();
        Long lastInteraction = playerInteractionCooldowns.get(playerId);
        
        if (lastInteraction == null) {
            return true;
        }
        
        long cooldownTicks = plugin.getConfigManager().getInteractionCooldown();
        long currentTime = System.currentTimeMillis();
        long cooldownMs = cooldownTicks * 50; // Convert ticks to milliseconds
        
        return (currentTime - lastInteraction) >= cooldownMs;
    }
    
    /**
     * Set the interaction cooldown for a player
     * 
     * @param player The player to set cooldown for
     */
    public void setPlayerInteractionCooldown(@NotNull Player player) {
        playerInteractionCooldowns.put(player.getUniqueId(), System.currentTimeMillis());
    }
    
    /**
     * Get all players within the auto-speak radius of any NPC
     * 
     * @return A map of NPCs to lists of nearby players
     */
    @NotNull
    public Map<SmithMasterNPC, List<Player>> getPlayersNearNPCs() {
        Map<SmithMasterNPC, List<Player>> nearbyPlayers = new HashMap<>();
        double radius = plugin.getConfigManager().getAutoSpeakRadius();
        
        for (SmithMasterNPC npc : npcs.values()) {
            if (!npc.isValid()) {
                continue;
            }
            
            Location npcLocation = npc.getLocation();
            if (npcLocation == null || npcLocation.getWorld() == null) {
                continue;
            }
            
            List<Player> playersInRange = new ArrayList<>();
            
            for (Player player : npcLocation.getWorld().getPlayers()) {
                if (player.getLocation().distance(npcLocation) <= radius) {
                    playersInRange.add(player);
                }
            }
            
            if (!playersInRange.isEmpty()) {
                nearbyPlayers.put(npc, playersInRange);
            }
        }
        
        return nearbyPlayers;
    }
    
    /**
     * Check if an entity is a Smith Master NPC
     * 
     * @param entity The entity to check
     * @return true if the entity is a Smith Master NPC
     */
    public boolean isSmithMasterNPC(@NotNull Entity entity) {
        return npcs.containsKey(entity.getUniqueId());
    }
    
    /**
     * Get the number of active NPCs
     * 
     * @return The number of active NPCs
     */
    public int getNPCCount() {
        return npcs.size();
    }
    
    /**
     * Clean up invalid NPCs (entities that no longer exist)
     */
    public void cleanupInvalidNPCs() {
        List<UUID> toRemove = new ArrayList<>();
        
        for (Map.Entry<UUID, SmithMasterNPC> entry : npcs.entrySet()) {
            if (!entry.getValue().isValid()) {
                toRemove.add(entry.getKey());
            }
        }
        
        for (UUID entityId : toRemove) {
            npcs.remove(entityId);
            plugin.getLogger().info("Cleaned up invalid NPC with ID: " + entityId);
        }
    }
    
    /**
     * Format a location for logging
     * 
     * @param location The location to format
     * @return A formatted string representation of the location
     */
    private String formatLocation(@NotNull Location location) {
        return String.format("World: %s, X: %.2f, Y: %.2f, Z: %.2f", 
            location.getWorld() != null ? location.getWorld().getName() : "null",
            location.getX(), 
            location.getY(), 
            location.getZ());
    }
}
