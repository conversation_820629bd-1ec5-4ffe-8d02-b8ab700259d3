@echo off
echo ========================================
echo Smith Master Plugin Build Script
echo Created by MyckasP for Luminary Kingdom
echo ========================================
echo.

:: Check if <PERSON><PERSON> is installed
mvn --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: <PERSON><PERSON> is not installed or not in PATH!
    echo Please install <PERSON><PERSON> and try again.
    pause
    exit /b 1
)

echo Maven found, starting build process...
echo.

:: Clean previous builds
echo [1/4] Cleaning previous builds...
mvn clean
if %errorlevel% neq 0 (
    echo ERROR: Failed to clean project!
    pause
    exit /b 1
)

:: Compile the project
echo.
echo [2/4] Compiling project...
mvn compile
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile project!
    pause
    exit /b 1
)

:: Run tests (if any)
echo.
echo [3/4] Running tests...
mvn test
if %errorlevel% neq 0 (
    echo WARNING: Some tests failed, but continuing build...
)

:: Package the plugin
echo.
echo [4/4] Packaging plugin...
mvn package
if %errorlevel% neq 0 (
    echo ERROR: Failed to package plugin!
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD SUCCESSFUL!
echo ========================================
echo.
echo The plugin JAR file has been created in the 'target' folder.
echo You can find it at: target\smith-master-1.0.0.jar
echo.
echo To install:
echo 1. Copy the JAR file to your server's plugins folder
echo 2. Restart your server
echo 3. Use /smithmaster help for available commands
echo.
echo For Luminary Kingdom server by MyckasP
echo ========================================

pause
