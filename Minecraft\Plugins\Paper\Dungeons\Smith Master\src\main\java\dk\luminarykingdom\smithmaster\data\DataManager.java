package dk.luminarykingdom.smithmaster.data;

import dk.luminarykingdom.smithmaster.SmithMasterPlugin;
import org.bukkit.Location;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Level;

/**
 * Manages persistent data storage for the Smith Master plugin
 * 
 * Handles saving and loading NPC locations, player data,
 * shop configurations, and other persistent information
 * in organized YAML files within the plugins folder.
 * 
 * <AUTHOR>
 */
public class DataManager {
    
    private final SmithMasterPlugin plugin;
    private final File dataFolder;
    
    // Data files
    private File npcDataFile;
    private File playerDataFile;
    private File shopDataFile;
    
    // Configurations
    private FileConfiguration npcData;
    private FileConfiguration playerData;
    private FileConfiguration shopData;
    
    public DataManager(@NotNull SmithMasterPlugin plugin) {
        this.plugin = plugin;
        this.dataFolder = new File(plugin.getDataFolder(), "data");
        
        initializeDataFiles();
    }
    
    /**
     * Initialize all data files and configurations
     */
    private void initializeDataFiles() {
        try {
            // Create data folder if it doesn't exist
            if (!dataFolder.exists()) {
                dataFolder.mkdirs();
            }
            
            // Initialize NPC data file
            npcDataFile = new File(dataFolder, "npcs.yml");
            if (!npcDataFile.exists()) {
                npcDataFile.createNewFile();
            }
            npcData = YamlConfiguration.loadConfiguration(npcDataFile);
            
            // Initialize player data file
            playerDataFile = new File(dataFolder, "players.yml");
            if (!playerDataFile.exists()) {
                playerDataFile.createNewFile();
            }
            playerData = YamlConfiguration.loadConfiguration(playerDataFile);
            
            // Initialize shop data file
            shopDataFile = new File(dataFolder, "shop.yml");
            if (!shopDataFile.exists()) {
                createDefaultShopData();
            }
            shopData = YamlConfiguration.loadConfiguration(shopDataFile);
            
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to initialize data files", e);
        }
    }
    
    /**
     * Create default shop data configuration
     */
    private void createDefaultShopData() {
        try {
            shopDataFile.createNewFile();
            FileConfiguration config = YamlConfiguration.loadConfiguration(shopDataFile);
            
            // Set default shop structure
            config.set("shop.version", "1.0");
            config.set("shop.last-updated", System.currentTimeMillis());
            config.set("shop.categories", List.of("weapons", "armor", "upgrades", "misc"));
            
            // Placeholder for future shop items
            config.set("items", new ArrayList<>());
            
            config.save(shopDataFile);
            
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to create default shop data", e);
        }
    }
    
    /**
     * Save NPC location data
     * 
     * @param npcId The NPC's unique ID
     * @param location The NPC's location
     */
    public void saveNPCLocation(@NotNull UUID npcId, @NotNull Location location) {
        try {
            String path = "npcs." + npcId.toString();
            
            npcData.set(path + ".world", location.getWorld() != null ? location.getWorld().getName() : "world");
            npcData.set(path + ".x", location.getX());
            npcData.set(path + ".y", location.getY());
            npcData.set(path + ".z", location.getZ());
            npcData.set(path + ".yaw", location.getYaw());
            npcData.set(path + ".pitch", location.getPitch());
            npcData.set(path + ".created", System.currentTimeMillis());
            npcData.set(path + ".active", true);
            
            saveNPCData();
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to save NPC location for " + npcId, e);
        }
    }
    
    /**
     * Load all saved NPC locations
     * 
     * @return A map of NPC IDs to their locations
     */
    @NotNull
    public Map<UUID, Location> loadNPCLocations() {
        Map<UUID, Location> locations = new HashMap<>();
        
        try {
            if (!npcData.contains("npcs")) {
                return locations;
            }
            
            for (String npcIdString : npcData.getConfigurationSection("npcs").getKeys(false)) {
                try {
                    UUID npcId = UUID.fromString(npcIdString);
                    String path = "npcs." + npcIdString;
                    
                    // Check if NPC is active
                    if (!npcData.getBoolean(path + ".active", true)) {
                        continue;
                    }
                    
                    String worldName = npcData.getString(path + ".world", "world");
                    double x = npcData.getDouble(path + ".x");
                    double y = npcData.getDouble(path + ".y");
                    double z = npcData.getDouble(path + ".z");
                    float yaw = (float) npcData.getDouble(path + ".yaw");
                    float pitch = (float) npcData.getDouble(path + ".pitch");
                    
                    org.bukkit.World world = plugin.getServer().getWorld(worldName);
                    if (world != null) {
                        Location location = new Location(world, x, y, z, yaw, pitch);
                        locations.put(npcId, location);
                    }
                    
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("Invalid NPC ID in data file: " + npcIdString);
                }
            }
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to load NPC locations", e);
        }
        
        return locations;
    }
    
    /**
     * Remove NPC data
     * 
     * @param npcId The NPC ID to remove
     */
    public void removeNPCData(@NotNull UUID npcId) {
        try {
            String path = "npcs." + npcId.toString();
            npcData.set(path + ".active", false);
            npcData.set(path + ".removed", System.currentTimeMillis());
            
            saveNPCData();
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to remove NPC data for " + npcId, e);
        }
    }
    
    /**
     * Save player interaction data
     * 
     * @param playerId The player's UUID
     * @param interactionCount The number of interactions
     * @param lastInteraction The timestamp of last interaction
     */
    public void savePlayerData(@NotNull UUID playerId, int interactionCount, long lastInteraction) {
        try {
            String path = "players." + playerId.toString();
            
            playerData.set(path + ".name", plugin.getServer().getOfflinePlayer(playerId).getName());
            playerData.set(path + ".interactions", interactionCount);
            playerData.set(path + ".last-interaction", lastInteraction);
            playerData.set(path + ".first-interaction", 
                playerData.getLong(path + ".first-interaction", System.currentTimeMillis()));
            
            savePlayerData();
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to save player data for " + playerId, e);
        }
    }
    
    /**
     * Load player interaction data
     * 
     * @param playerId The player's UUID
     * @return Player data, or null if not found
     */
    @Nullable
    public PlayerInteractionData loadPlayerData(@NotNull UUID playerId) {
        try {
            String path = "players." + playerId.toString();
            
            if (!playerData.contains(path)) {
                return null;
            }
            
            String name = playerData.getString(path + ".name", "Unknown");
            int interactions = playerData.getInt(path + ".interactions", 0);
            long lastInteraction = playerData.getLong(path + ".last-interaction", 0);
            long firstInteraction = playerData.getLong(path + ".first-interaction", 0);
            
            return new PlayerInteractionData(playerId, name, interactions, lastInteraction, firstInteraction);
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to load player data for " + playerId, e);
            return null;
        }
    }
    
    /**
     * Get all player interaction statistics
     * 
     * @return A list of all player interaction data
     */
    @NotNull
    public List<PlayerInteractionData> getAllPlayerData() {
        List<PlayerInteractionData> allData = new ArrayList<>();
        
        try {
            if (!playerData.contains("players")) {
                return allData;
            }
            
            for (String playerIdString : playerData.getConfigurationSection("players").getKeys(false)) {
                try {
                    UUID playerId = UUID.fromString(playerIdString);
                    PlayerInteractionData data = loadPlayerData(playerId);
                    if (data != null) {
                        allData.add(data);
                    }
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("Invalid player ID in data file: " + playerIdString);
                }
            }
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to load all player data", e);
        }
        
        return allData;
    }
    
    /**
     * Save NPC data to file
     */
    private void saveNPCData() {
        try {
            npcData.save(npcDataFile);
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to save NPC data file", e);
        }
    }
    
    /**
     * Save player data to file
     */
    private void savePlayerData() {
        try {
            playerData.save(playerDataFile);
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to save player data file", e);
        }
    }
    
    /**
     * Save shop data to file
     */
    private void saveShopData() {
        try {
            shopData.save(shopDataFile);
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to save shop data file", e);
        }
    }
    
    /**
     * Reload all data files
     */
    public void reloadData() {
        npcData = YamlConfiguration.loadConfiguration(npcDataFile);
        playerData = YamlConfiguration.loadConfiguration(playerDataFile);
        shopData = YamlConfiguration.loadConfiguration(shopDataFile);
    }
    
    /**
     * Get statistics about the data storage
     * 
     * @return Statistics string
     */
    @NotNull
    public String getStatistics() {
        int npcCount = npcData.contains("npcs") ? npcData.getConfigurationSection("npcs").getKeys(false).size() : 0;
        int playerCount = playerData.contains("players") ? playerData.getConfigurationSection("players").getKeys(false).size() : 0;
        
        return String.format("DataManager{npcs=%d, players=%d, data_folder='%s'}", 
            npcCount, playerCount, dataFolder.getAbsolutePath());
    }
    
    /**
     * Clean up old data entries
     * 
     * @param maxAge Maximum age in milliseconds
     */
    public void cleanupOldData(long maxAge) {
        long cutoffTime = System.currentTimeMillis() - maxAge;
        
        // Clean up old NPC data
        if (npcData.contains("npcs")) {
            for (String npcId : npcData.getConfigurationSection("npcs").getKeys(false)) {
                String path = "npcs." + npcId;
                long created = npcData.getLong(path + ".created", 0);
                boolean active = npcData.getBoolean(path + ".active", true);
                
                if (!active && created < cutoffTime) {
                    npcData.set(path, null);
                }
            }
            saveNPCData();
        }
        
        plugin.getLogger().info("Cleaned up old data entries older than " + (maxAge / 86400000) + " days");
    }
}
