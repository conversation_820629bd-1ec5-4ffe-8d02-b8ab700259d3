package dk.luminarykingdom.smithmaster.npc;

import dk.luminarykingdom.smithmaster.SmithMasterPlugin;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.TextDecoration;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Villager;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.UUID;

/**
 * Represents a Smith Master NPC entity
 * 
 * This class handles the creation, management, and behavior of the
 * Smith Master NPC including appearance, name, and basic properties.
 * 
 * <AUTHOR>
 */
public class SmithMasterNPC {
    
    private final SmithMasterPlugin plugin;
    private final Location spawnLocation;
    private Villager entity;
    private boolean isSpawned;
    
    public SmithMasterNPC(@NotNull SmithMasterPlugin plugin, @NotNull Location location) {
        this.plugin = plugin;
        this.spawnLocation = location.clone();
        this.isSpawned = false;
    }
    
    /**
     * Spawn the NPC at the specified location
     * 
     * @return true if spawning was successful
     */
    public boolean spawn() {
        try {
            if (isSpawned && entity != null && entity.isValid()) {
                return true; // Already spawned
            }
            
            // Spawn villager entity
            entity = (Villager) spawnLocation.getWorld().spawnEntity(spawnLocation, EntityType.VILLAGER);
            
            // Configure the villager
            setupNPCProperties();
            setupNPCAppearance();
            setupNPCBehavior();
            
            isSpawned = true;
            return true;
            
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to spawn Smith Master NPC: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Remove the NPC from the world
     */
    public void remove() {
        if (entity != null && entity.isValid()) {
            entity.remove();
        }
        isSpawned = false;
    }
    
    /**
     * Check if the NPC is valid and exists in the world
     * 
     * @return true if the NPC is valid
     */
    public boolean isValid() {
        return isSpawned && entity != null && entity.isValid();
    }
    
    /**
     * Get the NPC's current location
     * 
     * @return The NPC's location, or null if invalid
     */
    @Nullable
    public Location getLocation() {
        if (isValid()) {
            return entity.getLocation();
        }
        return null;
    }
    
    /**
     * Get the NPC's entity ID
     * 
     * @return The entity's UUID
     */
    @NotNull
    public UUID getEntityId() {
        if (entity != null) {
            return entity.getUniqueId();
        }
        return UUID.randomUUID(); // Fallback, should not happen
    }
    
    /**
     * Get the Bukkit entity
     * 
     * @return The villager entity, or null if invalid
     */
    @Nullable
    public Villager getEntity() {
        return isValid() ? entity : null;
    }
    
    /**
     * Setup basic NPC properties
     */
    private void setupNPCProperties() {
        if (entity == null) return;
        
        // Set custom name
        String npcName = plugin.getConfigManager().getNPCName();
        Component nameComponent = LegacyComponentSerializer.legacyAmpersand()
            .deserialize(npcName)
            .decoration(TextDecoration.ITALIC, false);
        
        entity.customName(nameComponent);
        entity.setCustomNameVisible(true);
        
        // Set basic properties
        entity.setAI(false); // Disable AI to prevent movement
        entity.setInvulnerable(true); // Make invulnerable
        entity.setSilent(false); // Allow sounds
        entity.setPersistent(true); // Don't despawn
        entity.setRemoveWhenFarAway(false); // Don't remove when players are far
        
        // Set villager profession to weaponsmith
        entity.setProfession(Villager.Profession.WEAPONSMITH);
        entity.setVillagerLevel(5); // Master level
    }
    
    /**
     * Setup NPC appearance and equipment
     */
    private void setupNPCAppearance() {
        if (entity == null) return;
        
        // Set villager type (plains villager for classic look)
        entity.setVillagerType(Villager.Type.PLAINS);
        
        // Give the villager some equipment to look like a smith
        ItemStack hammer = new ItemStack(Material.IRON_PICKAXE);
        ItemMeta hammerMeta = hammer.getItemMeta();
        if (hammerMeta != null) {
            hammerMeta.displayName(Component.text("Smith's Hammer")
                .decoration(TextDecoration.ITALIC, false));
            hammerMeta.setUnbreakable(true);
            hammer.setItemMeta(hammerMeta);
        }
        
        // Set main hand item
        entity.getEquipment().setItemInMainHand(hammer);
        entity.getEquipment().setItemInMainHandDropChance(0.0f);
    }
    
    /**
     * Setup NPC behavior and interaction settings
     */
    private void setupNPCBehavior() {
        if (entity == null) return;
        
        // Disable trading to prevent vanilla villager GUI
        entity.setRecipes(java.util.Collections.emptyList());
        
        // Set the villager to adult
        entity.setAdult();
        entity.setAgeLock(true);
        
        // Prevent breeding
        entity.setBreed(false);
        
        // Face towards players (this will be handled by the interaction listener)
        entity.setAware(true);
    }
    
    /**
     * Make the NPC look at a specific location
     * 
     * @param target The location to look at
     */
    public void lookAt(@NotNull Location target) {
        if (!isValid()) return;
        
        Location npcLoc = entity.getLocation();
        Location lookTarget = target.clone();
        
        // Calculate the direction vector
        double dx = lookTarget.getX() - npcLoc.getX();
        double dz = lookTarget.getZ() - npcLoc.getZ();
        
        // Calculate yaw (horizontal rotation)
        float yaw = (float) Math.toDegrees(Math.atan2(-dx, dz));
        
        // Set the entity's rotation
        npcLoc.setYaw(yaw);
        entity.teleport(npcLoc);
    }
    
    /**
     * Get the spawn location of this NPC
     * 
     * @return The original spawn location
     */
    @NotNull
    public Location getSpawnLocation() {
        return spawnLocation.clone();
    }
    
    /**
     * Check if this NPC matches the given entity
     * 
     * @param checkEntity The entity to check
     * @return true if the entity matches this NPC
     */
    public boolean matches(@NotNull org.bukkit.entity.Entity checkEntity) {
        return isValid() && entity.getUniqueId().equals(checkEntity.getUniqueId());
    }
    
    /**
     * Teleport the NPC to a new location
     * 
     * @param newLocation The new location
     * @return true if teleportation was successful
     */
    public boolean teleport(@NotNull Location newLocation) {
        if (!isValid()) return false;
        
        return entity.teleport(newLocation);
    }
    
    /**
     * Get debug information about this NPC
     * 
     * @return A string containing debug information
     */
    @NotNull
    public String getDebugInfo() {
        if (!isValid()) {
            return "SmithMasterNPC{invalid}";
        }
        
        Location loc = entity.getLocation();
        return String.format("SmithMasterNPC{id=%s, world=%s, x=%.2f, y=%.2f, z=%.2f, profession=%s, level=%d}", 
            entity.getUniqueId().toString().substring(0, 8),
            loc.getWorld() != null ? loc.getWorld().getName() : "null",
            loc.getX(), loc.getY(), loc.getZ(),
            entity.getProfession().name(),
            entity.getVillagerLevel());
    }
}
