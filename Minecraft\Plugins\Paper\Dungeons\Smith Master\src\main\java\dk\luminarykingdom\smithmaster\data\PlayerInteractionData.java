package dk.luminarykingdom.smithmaster.data;

import org.jetbrains.annotations.NotNull;

import java.util.Objects;
import java.util.UUID;

/**
 * Represents player interaction data for the Smith Master plugin
 * 
 * Stores information about player interactions with NPCs including
 * interaction counts, timestamps, and other relevant statistics.
 * 
 * <AUTHOR>
 */
public class PlayerInteractionData {
    
    private final UUID playerId;
    private final String playerName;
    private final int interactionCount;
    private final long lastInteractionTime;
    private final long firstInteractionTime;
    
    /**
     * Create new player interaction data
     * 
     * @param playerId The player's UUID
     * @param playerName The player's name
     * @param interactionCount The total number of interactions
     * @param lastInteractionTime The timestamp of the last interaction
     * @param firstInteractionTime The timestamp of the first interaction
     */
    public PlayerInteractionData(@NotNull UUID playerId, @NotNull String playerName, 
                               int interactionCount, long lastInteractionTime, 
                               long firstInteractionTime) {
        this.playerId = playerId;
        this.playerName = playerName;
        this.interactionCount = interactionCount;
        this.lastInteractionTime = lastInteractionTime;
        this.firstInteractionTime = firstInteractionTime;
    }
    
    /**
     * Get the player's UUID
     * 
     * @return The player's UUID
     */
    @NotNull
    public UUID getPlayerId() {
        return playerId;
    }
    
    /**
     * Get the player's name
     * 
     * @return The player's name
     */
    @NotNull
    public String getPlayerName() {
        return playerName;
    }
    
    /**
     * Get the total number of interactions
     * 
     * @return The interaction count
     */
    public int getInteractionCount() {
        return interactionCount;
    }
    
    /**
     * Get the timestamp of the last interaction
     * 
     * @return The last interaction timestamp
     */
    public long getLastInteractionTime() {
        return lastInteractionTime;
    }
    
    /**
     * Get the timestamp of the first interaction
     * 
     * @return The first interaction timestamp
     */
    public long getFirstInteractionTime() {
        return firstInteractionTime;
    }
    
    /**
     * Get the time since the last interaction in milliseconds
     * 
     * @return Time since last interaction
     */
    public long getTimeSinceLastInteraction() {
        return System.currentTimeMillis() - lastInteractionTime;
    }
    
    /**
     * Get the total time span of interactions in milliseconds
     * 
     * @return Time span from first to last interaction
     */
    public long getInteractionTimeSpan() {
        return lastInteractionTime - firstInteractionTime;
    }
    
    /**
     * Check if the player has interacted recently
     * 
     * @param recentThreshold The threshold in milliseconds for "recent"
     * @return true if the player has interacted recently
     */
    public boolean hasInteractedRecently(long recentThreshold) {
        return getTimeSinceLastInteraction() <= recentThreshold;
    }
    
    /**
     * Check if this is a new player (first interaction)
     * 
     * @return true if this is the player's first interaction
     */
    public boolean isNewPlayer() {
        return interactionCount <= 1;
    }
    
    /**
     * Check if this is a frequent user
     * 
     * @param frequentThreshold The minimum interactions to be considered frequent
     * @return true if the player is a frequent user
     */
    public boolean isFrequentUser(int frequentThreshold) {
        return interactionCount >= frequentThreshold;
    }
    
    /**
     * Create a new PlayerInteractionData with incremented interaction count
     * 
     * @return A new instance with incremented count and updated last interaction time
     */
    @NotNull
    public PlayerInteractionData withNewInteraction() {
        return new PlayerInteractionData(
            playerId, 
            playerName, 
            interactionCount + 1, 
            System.currentTimeMillis(), 
            firstInteractionTime
        );
    }
    
    /**
     * Format the interaction data for display
     * 
     * @return A formatted string representation
     */
    @NotNull
    public String formatForDisplay() {
        long timeSince = getTimeSinceLastInteraction();
        String timeString = formatTime(timeSince);
        
        return String.format("§6%s §7- §e%d interactions §7(last: %s ago)", 
            playerName, interactionCount, timeString);
    }
    
    /**
     * Format time duration for display
     * 
     * @param milliseconds The time in milliseconds
     * @return A formatted time string
     */
    private String formatTime(long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return days + "d";
        } else if (hours > 0) {
            return hours + "h";
        } else if (minutes > 0) {
            return minutes + "m";
        } else {
            return seconds + "s";
        }
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        PlayerInteractionData that = (PlayerInteractionData) obj;
        return interactionCount == that.interactionCount &&
               lastInteractionTime == that.lastInteractionTime &&
               firstInteractionTime == that.firstInteractionTime &&
               Objects.equals(playerId, that.playerId) &&
               Objects.equals(playerName, that.playerName);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(playerId, playerName, interactionCount, lastInteractionTime, firstInteractionTime);
    }
    
    @Override
    public String toString() {
        return String.format("PlayerInteractionData{playerId=%s, name='%s', interactions=%d, lastInteraction=%d, firstInteraction=%d}", 
            playerId, playerName, interactionCount, lastInteractionTime, firstInteractionTime);
    }
}
