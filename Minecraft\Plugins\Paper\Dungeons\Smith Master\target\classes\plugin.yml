name: SmithMaster
version: '1.0.0'
main: dk.luminarykingdom.smithmaster.SmithMasterPlugin
api-version: '1.20'
prefix: SmithMaster
authors: [MyckasP]
description: A Minecraft Paper plugin for the Luminary Kingdom dungeon server featuring an interactive NPC smith
website: https://luminarykingdom.dk

commands:
  smithmaster:
    description: Main command for Smith Master plugin
    usage: /<command> [reload|spawn|remove|help]
    permission: smithmaster.admin
    aliases: [sm, smith]

permissions:
  smithmaster.admin:
    description: Allows access to all Smith Master admin commands
    default: op
  smithmaster.use:
    description: Allows players to interact with the Smith Master NPC
    default: true
  smithmaster.shop:
    description: Allows players to access the Smith Master shop
    default: true
  smithmaster.reload:
    description: Allows reloading the plugin configuration
    default: op
